// Simplified SDF shader with DOTS instancing support:
// - No Shading Option (bevel / bump / env map)
// - No Glow Option
// - Softness is applied on both side of the outline
// - DOTS instancing compatible for BatchRendererGroup

Shader "FairyGUI/TextMeshPro/Distance Field-DOTS" {

Properties {
	_FaceColor     ("Face Color", Color) = (1,1,1,1)
	_FaceDilate			("Face Dilate", Range(-1,1)) = 0

	_OutlineColor	("Outline Color", Color) = (0,0,0,1)
	_OutlineWidth		("Outline Thickness", Range(0,1)) = 0
	_OutlineSoftness	("Outline Softness", Range(0,1)) = 0

	_UnderlayColor	("Border Color", Color) = (0,0,0,.5)
	_UnderlayOffsetX 	("Border OffsetX", Range(-1,1)) = 0
	_UnderlayOffsetY 	("Border OffsetY", Range(-1,1)) = 0
	_UnderlayDilate		("Border Dilate", Range(-1,1)) = 0
	_UnderlaySoftness 	("Border Softness", Range(0,1)) = 0

	_WeightNormal		("Weight Normal", float) = 0
	_WeightBold			("Weight Bold", float) = .5

	_ShaderFlags		("Flags", float) = 0
	_ScaleRatioA		("Scale RatioA", float) = 1
	_ScaleRatioB		("Scale RatioB", float) = 1
	_ScaleRatioC		("Scale RatioC", float) = 1

	_MainTex			("Font Atlas", 2D) = "white" {}
	_TextureWidth		("Texture Width", float) = 512
	_TextureHeight		("Texture Height", float) = 512
	_GradientScale		("Gradient Scale", float) = 5
	_ScaleX				("Scale X", float) = 1
	_ScaleY				("Scale Y", float) = 1
	_PerspectiveFilter	("Perspective Correction", Range(0, 1)) = 0.875
	_Sharpness			("Sharpness", Range(-1,1)) = 0

	_VertexOffsetX		("Vertex OffsetX", float) = 0
	_VertexOffsetY		("Vertex OffsetY", float) = 0

	_ClipRect			("Clip Rect", vector) = (-32767, -32767, 32767, 32767)
	_MaskSoftnessX		("Mask SoftnessX", float) = 0
	_MaskSoftnessY		("Mask SoftnessY", float) = 0

	_StencilComp		("Stencil Comparison", Float) = 8
	_Stencil			("Stencil ID", Float) = 0
	_StencilOp			("Stencil Operation", Float) = 0
	_StencilWriteMask	("Stencil Write Mask", Float) = 255
	_StencilReadMask	("Stencil Read Mask", Float) = 255

	_CullMode			("Cull Mode", Float) = 0
	_ColorMask			("Color Mask", Float) = 15
	_ClipBox			("Clip Box", vector) = (-2, -2, 0, 0)
}

SubShader {
	Tags
	{
		"Queue"="Transparent"
		"IgnoreProjector"="True"
		"RenderType"="Transparent"
		"RenderPipeline"="UniversalPipeline"
		"LightMode" = "UniversalForward"
	}

	Stencil
	{
		Ref [_Stencil]
		Comp [_StencilComp]
		Pass [_StencilOp]
		ReadMask [_StencilReadMask]
		WriteMask [_StencilWriteMask]
	}

	Cull [_CullMode]
	ZWrite Off
	Lighting Off
	Fog { Mode Off }
	Blend One OneMinusSrcAlpha
	ColorMask [_ColorMask]
	ZTest Always
	
	Pass {
		HLSLPROGRAM
		#pragma exclude_renderers gles gles3 glcore
		#pragma target 4.5
		
		// DOTS instancing support
		#pragma multi_compile_instancing
		#pragma instancing_options renderinglayer
		#pragma multi_compile _ DOTS_INSTANCING_ON
		
		// Original shader features and multi_compile directives
		#pragma shader_feature_local UNDERLAY_ON UNDERLAY_INNER
		#pragma multi_compile __ OUTLINE_ON
		#pragma multi_compile NOT_GRAYED GRAYED
		#pragma multi_compile NOT_CLIPPED CLIPPED
		#pragma multi_compile_local _ ENABLE_HUD_TEXT
		#pragma multi_compile_local _ ENABLE_WORLD_HUD_SCALE

		#pragma vertex VertShader
		#pragma fragment PixShader
		#pragma enable_d3d11_debug_symbols
		#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

		struct vertex_t {
			float4	vertex			: POSITION;
			float3	normal			: NORMAL;
			half4	color			: COLOR;
			float2	texcoord0		: TEXCOORD0;
			float2	texcoord1		: TEXCOORD1;
			#ifdef DOTS_INSTANCING_ON
			UNITY_VERTEX_INPUT_INSTANCE_ID
			#endif
		};

		struct pixel_t {
			float4	vertex			: SV_POSITION;
			half4	faceColor		: COLOR;
			half4	outlineColor	: COLOR1;
			float2	texcoord0		: TEXCOORD0;			// Texture UV, Mask UV
			half4	param			: TEXCOORD1;			// Scale(x), BiasIn(y), BiasOut(z), Bias(w)
			half2	mask			: TEXCOORD2;			// Position in clip space(xy), Softness(zw)
			#if (UNDERLAY_ON | UNDERLAY_INNER)
			float4	texcoord1		: TEXCOORD3;			// Texture UV, alpha, reserved
			half2	underlayParam	: TEXCOORD4;			// Scale(x), Bias(y)
			#endif
			#ifdef DOTS_INSTANCING_ON
			UNITY_VERTEX_INPUT_INSTANCE_ID
			#endif
			UNITY_VERTEX_OUTPUT_STEREO
		};

		CBUFFER_START(UnityPerMaterial)
		    half4 _FaceColor;				// RGBA : Color + Opacity
			float _FaceDilate;				// v[ 0, 1]
			float _OutlineSoftness;			// v[ 0, 1]
			float _OutlineWidth;			// v[ 0, 1]
			half4 _OutlineColor;			// RGBA : Color + Opacity
    		float _ScaleRatioA;
    		float _ScaleRatioC;
    		float _VertexOffsetX;
    		float _VertexOffsetY;
    		float _GradientScale;
    		float _PerspectiveFilter;
    		float _ScaleX;
    		float _ScaleY;
    		float _Sharpness;
    		float _WeightNormal;
    		float _WeightBold;
    		float _MaskSoftnessX;
    		float _MaskSoftnessY;
    		half4 _UnderlayColor;			// RGBA : Color + Opacity
    		float _UnderlayOffsetX;			// v[-1, 1]
   			float _UnderlayOffsetY;			// v[-1, 1]
    		float _UnderlayDilate;			// v[-1, 1]
    		float _UnderlaySoftness;		// v[ 0, 1]
    		float _TextureWidth;
    		float _TextureHeight;
			vector _ClipBox;
		CBUFFER_END
		
		// DOTS instancing properties
		#ifdef DOTS_INSTANCING_ON
			UNITY_DOTS_INSTANCING_START(MaterialPropertyMetadata)
				UNITY_DOTS_INSTANCED_PROP(float4, _FaceColor)
				UNITY_DOTS_INSTANCED_PROP(float4, _OutlineColor)
				UNITY_DOTS_INSTANCED_PROP(float4, _UnderlayColor)
				UNITY_DOTS_INSTANCED_PROP(float, _FaceDilate)
				UNITY_DOTS_INSTANCED_PROP(float, _OutlineWidth)
				UNITY_DOTS_INSTANCED_PROP(float, _OutlineSoftness)
				UNITY_DOTS_INSTANCED_PROP(float, _VertexOffsetX)
				UNITY_DOTS_INSTANCED_PROP(float, _VertexOffsetY)
				UNITY_DOTS_INSTANCED_PROP(float4, _ClipBox)
			UNITY_DOTS_INSTANCING_END(MaterialPropertyMetadata)
			#define _FaceColor UNITY_ACCESS_DOTS_INSTANCED_PROP_WITH_DEFAULT(float4, _FaceColor)
			#define _OutlineColor UNITY_ACCESS_DOTS_INSTANCED_PROP_WITH_DEFAULT(float4, _OutlineColor)
			#define _UnderlayColor UNITY_ACCESS_DOTS_INSTANCED_PROP_WITH_DEFAULT(float4, _UnderlayColor)
			#define _FaceDilate UNITY_ACCESS_DOTS_INSTANCED_PROP_WITH_DEFAULT(float, _FaceDilate)
			#define _OutlineWidth UNITY_ACCESS_DOTS_INSTANCED_PROP_WITH_DEFAULT(float, _OutlineWidth)
			#define _OutlineSoftness UNITY_ACCESS_DOTS_INSTANCED_PROP_WITH_DEFAULT(float, _OutlineSoftness)
			#define _VertexOffsetX UNITY_ACCESS_DOTS_INSTANCED_PROP_WITH_DEFAULT(float, _VertexOffsetX)
			#define _VertexOffsetY UNITY_ACCESS_DOTS_INSTANCED_PROP_WITH_DEFAULT(float, _VertexOffsetY)
			#define _ClipBox UNITY_ACCESS_DOTS_INSTANCED_PROP_WITH_DEFAULT(float4, _ClipBox)
		#endif

		TEXTURE2D(_MainTex);
		SAMPLER(sampler_MainTex);
		
		float _GlobalHudGradientScale; //
		int LOWQuality_LinearMode;

		inline half3 GammaToLinearSpace (half3 sRGB)
		{
		    // Approximate version from http://chilliant.blogspot.com.au/2012/08/srgb-approximations-for-hlsl.html?m=1
		    return sRGB * (sRGB * (sRGB * 0.305306011h + 0.682171111h) + 0.012522878h);
		}

		inline half3 LinearToGammaSpace (half3 linRGB)
		{
		    linRGB = max(linRGB, half3(0.h, 0.h, 0.h));
		    // An almost-perfect approximation from http://chilliant.blogspot.com.au/2012/08/srgb-approximations-for-hlsl.html?m=1
		    return max(1.055h * pow(linRGB, 0.416666667h) - 0.055h, 0.h);
		}

		pixel_t VertShader(vertex_t input)
		{
			pixel_t output = (pixel_t)0;

			// DOTS instancing setup
			#ifdef DOTS_INSTANCING_ON
			UNITY_SETUP_INSTANCE_ID(input);
			UNITY_TRANSFER_INSTANCE_ID(input, output);
			#endif
			UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);

			float bold = step(input.texcoord1.y, 0);
			float4 vert = input.vertex;

			float _FinalGradientScale = _GradientScale;

			#if ENABLE_HUD_TEXT
				_FinalGradientScale = _GlobalHudGradientScale;
			#endif

			vert.x += _VertexOffsetX;
			vert.y += _VertexOffsetY;
			float4 vPosition = TransformObjectToHClip(vert.xyz);

			float2 pixelSize = vPosition.w;
			pixelSize /= float2(_ScaleX, _ScaleY) * abs(mul((float2x2)UNITY_MATRIX_P, _ScreenParams.xy));

			float scale = rsqrt(dot(pixelSize, pixelSize));
			scale *= abs(input.texcoord1.y) * _FinalGradientScale* (_Sharpness + 1);
			if(UNITY_MATRIX_P[3][3] == 0) scale = lerp(abs(scale) * (1 - _PerspectiveFilter), scale, abs(dot(TransformObjectToWorldNormal(input.normal.xyz), normalize(GetWorldSpaceViewDir(vert.xyz)))));

			float weight = lerp(_WeightNormal, _WeightBold, bold) / 4.0;
			weight = (weight + _FaceDilate) * _ScaleRatioA * 0.5;

			float layerScale = scale;

			scale /= 1 + (_OutlineSoftness * _ScaleRatioA * scale);
			float bias = (0.5 - weight) * scale - 0.5;
			float outline = _OutlineWidth * _ScaleRatioA * 0.5 * scale;

			float opacity = input.color.a;
			#if (UNDERLAY_ON | UNDERLAY_INNER)
			opacity = 1.0;
			#endif

			_FaceColor.rgb = lerp(GammaToLinearSpace(_FaceColor.rgb), _FaceColor.rgb, LOWQuality_LinearMode);
			half4 faceColor = half4(input.color.rgb, opacity) * _FaceColor;

			_OutlineColor.rgb = LinearToGammaSpace(_OutlineColor.rgb);
			half4 outlineColor = _OutlineColor;
			outlineColor.a *= opacity;
			outlineColor.rgb *= outlineColor.a;
			outlineColor = lerp(faceColor, outlineColor, sqrt(min(1.0, (outline * 2))));

			#if (UNDERLAY_ON | UNDERLAY_INNER)
			layerScale /= 1 + ((_UnderlaySoftness * _ScaleRatioC) * layerScale);
			float layerBias = (.5 - weight) * layerScale - .5 - ((_UnderlayDilate * _ScaleRatioC) * .5 * layerScale);

			float x = -(_UnderlayOffsetX * _ScaleRatioC) * _FinalGradientScale / _TextureWidth;
			float y = -(_UnderlayOffsetY * _ScaleRatioC) * _FinalGradientScale / _TextureHeight;
			float2 layerOffset = float2(x, y);
			#endif

			// Populate structure for pixel shader
			output.vertex = vPosition;
			output.faceColor = faceColor;
			output.outlineColor = outlineColor;
			output.texcoord0 = input.texcoord0;
			output.param = half4(scale, bias - outline, bias + outline, bias);
			#if (UNDERLAY_ON || UNDERLAY_INNER)
			output.texcoord1 = float4(input.texcoord0 + layerOffset, input.color.a, 0);
			output.underlayParam = half2(layerScale, layerBias);
			#endif
			
			#ifdef CLIPPED
			output.mask = TransformObjectToWorld(input.vertex.xyz).xy * _ClipBox.zw + _ClipBox.xy;
			#endif

			return output;
		}

		// PIXEL SHADER
		half4 PixShader(pixel_t input) : SV_Target
		{
			// DOTS instancing setup
			#ifdef DOTS_INSTANCING_ON
			UNITY_SETUP_INSTANCE_ID(input);
			#endif

			half d = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, input.texcoord0.xy).a * input.param.x;
			half4 c = input.faceColor * saturate(d - input.param.w);

			#ifdef OUTLINE_ON
			c = lerp(input.outlineColor, input.faceColor, saturate(d - input.param.z));
			c *= saturate(d - input.param.y);
			#endif

			#if UNDERLAY_ON
			d = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, input.texcoord1.xy).a * input.underlayParam.x;
			c += float4(_UnderlayColor.rgb * _UnderlayColor.a, _UnderlayColor.a) * saturate(d - input.underlayParam.y) * (1 - c.a);
			#endif

			#if UNDERLAY_INNER
			half sd = saturate(d - input.param.z);
			d = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, input.texcoord1.xy).a * input.underlayParam.x;
			c += float4(_UnderlayColor.rgb * _UnderlayColor.a, _UnderlayColor.a) * (1 - saturate(d - input.underlayParam.y)) * sd * (1 - c.a);
			#endif

			#if (UNDERLAY_ON | UNDERLAY_INNER)
			c *= input.texcoord1.z;
			#endif

			#ifdef GRAYED
			half grey = dot(c.rgb, half3(0.299, 0.587, 0.114));
			c.rgb = half3(grey, grey, grey); 
			#endif

			#ifdef CLIPPED
			float2 factor = abs(input.mask);
			clip(1-max(factor.x, factor.y));
			#endif

			//save shader variant
			c.rgb = lerp(c.rgb, c.rgb * c.rgb, LOWQuality_LinearMode);
			
			return c;
		}
		ENDHLSL
	}
}

 CustomEditor "TMPro.EditorUtilities.TMP_SDFShaderGUI"
}
