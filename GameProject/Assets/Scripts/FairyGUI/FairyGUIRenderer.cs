using System;
using System.Collections.Generic;
using Unity.Collections;
using Unity.Collections.LowLevel.Unsafe;
using Unity.Jobs;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.Rendering;

namespace FairyGUI
{
    /// <summary>
    /// FairyGUI渲染数据结构
    /// </summary>
    public class FairyGUIRenderData
    {
        public Mesh mesh;
        public Material material;
        public Matrix4x4 localToWorldMatrix;
        public int renderOrder;
        public Color color = Color.white;
        public Vector4 clipBox = new Vector4(-2, -2, 0, 0);
        public float baseColor = 0f;
        public float linera2Gamma = 0f; // For ImageRT shader
        
        // BatchRendererGroup相关
        public BatchID batchId;
        public BatchMeshID meshId;
        public BatchMaterialID materialId;
        public GraphicsBuffer instanceData;
        
        // FairyGUI特定属性
        public string shaderType; // "Image", "ImageRT", "TMP"
        public bool isVisible = true;
        
        // TMP特定属性
        public Color faceColor = Color.white;
        public Color outlineColor = Color.black;
        public Color underlayColor = new Color(0, 0, 0, 0.5f);
        public float faceDilate = 0f;
        public float outlineWidth = 0f;
        public float outlineSoftness = 0f;
        public float vertexOffsetX = 0f;
        public float vertexOffsetY = 0f;
    }

    /// <summary>
    /// FairyGUI BatchRendererGroup渲染器
    /// </summary>
    public class FairyGUIRenderer : IDisposable
    {
        private BatchRendererGroup m_BRG;
        private List<FairyGUIRenderData> m_RenderDataList;
        private bool m_IsInitialized = false;
        private bool m_UseBRG = true;

        // Shader属性ID缓存
        private static readonly int s_BaseColorPropertyId = Shader.PropertyToID("_BaseColor");
        private static readonly int s_ClipBoxPropertyId = Shader.PropertyToID("_ClipBox");
        private static readonly int s_Linera2GammaPropertyId = Shader.PropertyToID("_Linera2Gamma");
        private static readonly int s_FaceColorPropertyId = Shader.PropertyToID("_FaceColor");
        private static readonly int s_OutlineColorPropertyId = Shader.PropertyToID("_OutlineColor");
        private static readonly int s_UnderlayColorPropertyId = Shader.PropertyToID("_UnderlayColor");
        private static readonly int s_FaceDilatePropertyId = Shader.PropertyToID("_FaceDilate");
        private static readonly int s_OutlineWidthPropertyId = Shader.PropertyToID("_OutlineWidth");
        private static readonly int s_OutlineSoftnessPropertyId = Shader.PropertyToID("_OutlineSoftness");
        private static readonly int s_VertexOffsetXPropertyId = Shader.PropertyToID("_VertexOffsetX");
        private static readonly int s_VertexOffsetYPropertyId = Shader.PropertyToID("_VertexOffsetY");

        public FairyGUIRenderer(bool useBRG = true)
        {
            m_UseBRG = useBRG;
            m_RenderDataList = new List<FairyGUIRenderData>();
            
            if (m_UseBRG)
            {
                Initialize();
            }
        }

        private void Initialize()
        {
            if (m_IsInitialized) return;

            try
            {
                m_BRG = new BatchRendererGroup(OnPerformCulling, IntPtr.Zero);
                m_IsInitialized = true;
                Debug.Log("FairyGUIRenderer: BatchRendererGroup initialized successfully");
            }
            catch (Exception e)
            {
                Debug.LogError($"FairyGUIRenderer: Failed to initialize BatchRendererGroup: {e.Message}");
                m_UseBRG = false;
            }
        }

        /// <summary>
        /// 添加渲染数据
        /// </summary>
        public void AddRenderData(FairyGUIRenderData renderData)
        {
            if (renderData == null) return;

            m_RenderDataList.Add(renderData);

            if (m_UseBRG && m_IsInitialized)
            {
                RegisterRenderDataWithBRG(renderData);
            }
        }

        /// <summary>
        /// 移除渲染数据
        /// </summary>
        public void RemoveRenderData(FairyGUIRenderData renderData)
        {
            if (renderData == null) return;

            if (m_UseBRG && m_IsInitialized && renderData.batchId.value != 0)
            {
                m_BRG.RemoveBatch(renderData.batchId);
            }

            if (renderData.instanceData != null)
            {
                renderData.instanceData.Release();
                renderData.instanceData = null;
            }

            m_RenderDataList.Remove(renderData);
        }

        /// <summary>
        /// 清空所有渲染数据
        /// </summary>
        public void Clear()
        {
            foreach (var renderData in m_RenderDataList)
            {
                if (m_UseBRG && m_IsInitialized && renderData.batchId.value != 0)
                {
                    m_BRG.RemoveBatch(renderData.batchId);
                }

                if (renderData.instanceData != null)
                {
                    renderData.instanceData.Release();
                }
            }

            m_RenderDataList.Clear();
        }

        /// <summary>
        /// 注册渲染数据到BatchRendererGroup
        /// </summary>
        private void RegisterRenderDataWithBRG(FairyGUIRenderData renderData)
        {
            if (!m_IsInitialized || renderData.mesh == null || renderData.material == null)
                return;

            try
            {
                // 注册网格和材质
                renderData.meshId = m_BRG.RegisterMesh(renderData.mesh);
                renderData.materialId = m_BRG.RegisterMaterial(renderData.material);

                // 创建实例数据缓冲区
                CreateInstanceDataBuffer(renderData);

                Debug.Log($"FairyGUIRenderer: Registered mesh: {renderData.meshId}, material: {renderData.materialId} ({renderData.material.name})");
            }
            catch (Exception e)
            {
                Debug.LogError($"FairyGUIRenderer: Failed to register render data: {e.Message}");
            }
        }

        /// <summary>
        /// 创建实例数据缓冲区
        /// </summary>
        private void CreateInstanceDataBuffer(FairyGUIRenderData renderData)
        {
            // 根据shader类型确定需要的属性
            var metadata = new NativeArray<MetadataValue>(GetMetadataCount(renderData.shaderType), Allocator.Temp);
            int metadataIndex = 0;

            // 通用变换矩阵 (必需)
            metadata[metadataIndex++] = CreateMetadataValue(BatchRendererGroupGlobals.kBuiltinPropertyUnity_ObjectToWorld, 0, true);
            metadata[metadataIndex++] = CreateMetadataValue(BatchRendererGroupGlobals.kBuiltinPropertyUnity_WorldToObject, 64, true);

            // 根据shader类型添加特定属性
            switch (renderData.shaderType)
            {
                case "Image":
                    metadata[metadataIndex++] = CreateMetadataValue(s_BaseColorPropertyId, 128, true);
                    metadata[metadataIndex++] = CreateMetadataValue(s_ClipBoxPropertyId, 132, true);
                    break;
                    
                case "ImageRT":
                    metadata[metadataIndex++] = CreateMetadataValue(s_BaseColorPropertyId, 128, true);
                    metadata[metadataIndex++] = CreateMetadataValue(s_ClipBoxPropertyId, 132, true);
                    metadata[metadataIndex++] = CreateMetadataValue(s_Linera2GammaPropertyId, 148, true);
                    break;
                    
                case "TMP":
                    metadata[metadataIndex++] = CreateMetadataValue(s_FaceColorPropertyId, 128, true);
                    metadata[metadataIndex++] = CreateMetadataValue(s_OutlineColorPropertyId, 144, true);
                    metadata[metadataIndex++] = CreateMetadataValue(s_UnderlayColorPropertyId, 160, true);
                    metadata[metadataIndex++] = CreateMetadataValue(s_FaceDilatePropertyId, 176, true);
                    metadata[metadataIndex++] = CreateMetadataValue(s_OutlineWidthPropertyId, 180, true);
                    metadata[metadataIndex++] = CreateMetadataValue(s_OutlineSoftnessPropertyId, 184, true);
                    metadata[metadataIndex++] = CreateMetadataValue(s_VertexOffsetXPropertyId, 188, true);
                    metadata[metadataIndex++] = CreateMetadataValue(s_VertexOffsetYPropertyId, 192, true);
                    metadata[metadataIndex++] = CreateMetadataValue(s_ClipBoxPropertyId, 196, true);
                    break;
            }

            // 创建GraphicsBuffer
            int bufferSize = GetInstanceDataSize(renderData.shaderType);
            renderData.instanceData = new GraphicsBuffer(GraphicsBuffer.Target.Raw, bufferSize / 4, 4);

            // 填充实例数据
            PopulateInstanceData(renderData);

            // 创建批次
            renderData.batchId = m_BRG.AddBatch(metadata, renderData.instanceData.bufferHandle);

            metadata.Dispose();
        }

        /// <summary>
        /// 填充实例数据
        /// </summary>
        private void PopulateInstanceData(FairyGUIRenderData renderData)
        {
            int dataSize = GetInstanceDataSize(renderData.shaderType);
            var data = new NativeArray<float>(dataSize / 4, Allocator.Temp);

            // 变换矩阵 (0-63字节)
            var matrix = renderData.localToWorldMatrix;
            for (int i = 0; i < 16; i++)
            {
                data[i] = matrix[i];
            }

            // 逆变换矩阵 (64-127字节)
            var inverseMatrix = renderData.localToWorldMatrix.inverse;
            for (int i = 0; i < 16; i++)
            {
                data[16 + i] = inverseMatrix[i];
            }

            // 根据shader类型填充特定数据
            switch (renderData.shaderType)
            {
                case "Image":
                    data[32] = renderData.baseColor; // _BaseColor (128字节)
                    data[33] = renderData.clipBox.x; // _ClipBox (132字节)
                    data[34] = renderData.clipBox.y;
                    data[35] = renderData.clipBox.z;
                    data[36] = renderData.clipBox.w;
                    break;
                    
                case "ImageRT":
                    data[32] = renderData.baseColor; // _BaseColor (128字节)
                    data[33] = renderData.clipBox.x; // _ClipBox (132字节)
                    data[34] = renderData.clipBox.y;
                    data[35] = renderData.clipBox.z;
                    data[36] = renderData.clipBox.w;
                    data[37] = renderData.linera2Gamma; // _Linera2Gamma (148字节)
                    break;
                    
                case "TMP":
                    // _FaceColor (128字节)
                    data[32] = renderData.faceColor.r;
                    data[33] = renderData.faceColor.g;
                    data[34] = renderData.faceColor.b;
                    data[35] = renderData.faceColor.a;
                    // _OutlineColor (144字节)
                    data[36] = renderData.outlineColor.r;
                    data[37] = renderData.outlineColor.g;
                    data[38] = renderData.outlineColor.b;
                    data[39] = renderData.outlineColor.a;
                    // _UnderlayColor (160字节)
                    data[40] = renderData.underlayColor.r;
                    data[41] = renderData.underlayColor.g;
                    data[42] = renderData.underlayColor.b;
                    data[43] = renderData.underlayColor.a;
                    // 其他属性
                    data[44] = renderData.faceDilate; // _FaceDilate (176字节)
                    data[45] = renderData.outlineWidth; // _OutlineWidth (180字节)
                    data[46] = renderData.outlineSoftness; // _OutlineSoftness (184字节)
                    data[47] = renderData.vertexOffsetX; // _VertexOffsetX (188字节)
                    data[48] = renderData.vertexOffsetY; // _VertexOffsetY (192字节)
                    // _ClipBox (196字节)
                    data[49] = renderData.clipBox.x;
                    data[50] = renderData.clipBox.y;
                    data[51] = renderData.clipBox.z;
                    data[52] = renderData.clipBox.w;
                    break;
            }

            renderData.instanceData.SetData(data);
            data.Dispose();
        }

        /// <summary>
        /// 获取元数据数量
        /// </summary>
        private int GetMetadataCount(string shaderType)
        {
            switch (shaderType)
            {
                case "Image": return 4; // ObjectToWorld + WorldToObject + BaseColor + ClipBox
                case "ImageRT": return 5; // Image + Linera2Gamma
                case "TMP": return 11; // ObjectToWorld + WorldToObject + 9个TMP属性
                default: return 4;
            }
        }

        /// <summary>
        /// 获取实例数据大小（字节）
        /// </summary>
        private int GetInstanceDataSize(string shaderType)
        {
            switch (shaderType)
            {
                case "Image": return 148; // 128 + 4 + 16
                case "ImageRT": return 152; // 148 + 4
                case "TMP": return 212; // 128 + 84 (21个float)
                default: return 148;
            }
        }

        /// <summary>
        /// 创建元数据值
        /// </summary>
        private MetadataValue CreateMetadataValue(int nameID, uint gpuAddress, bool isOverridden)
        {
            return new MetadataValue
            {
                NameID = nameID,
                Value = 0x80000000 | gpuAddress,
            };
        }

        /// <summary>
        /// BatchRendererGroup裁剪回调
        /// </summary>
        public unsafe JobHandle OnPerformCulling(
            BatchRendererGroup rendererGroup,
            BatchCullingContext cullingContext,
            BatchCullingOutput cullingOutput,
            IntPtr userContext)
        {
            if (m_RenderDataList.Count == 0)
            {
                return new JobHandle();
            }

            // 过滤可见的渲染数据
            var visibleRenderData = new List<FairyGUIRenderData>();
            foreach (var renderData in m_RenderDataList)
            {
                if (renderData.isVisible && renderData.batchId.value != 0)
                {
                    visibleRenderData.Add(renderData);
                }
            }

            if (visibleRenderData.Count == 0)
            {
                return new JobHandle();
            }

            // 按渲染顺序排序以保持FairyGUI的渲染顺序
            visibleRenderData.Sort((a, b) => a.renderOrder.CompareTo(b.renderOrder));

            int alignment = UnsafeUtility.AlignOf<long>();
            var drawCommands = (BatchCullingOutputDrawCommands*)cullingOutput.drawCommands.GetUnsafePtr();

            int numDrawCommands = visibleRenderData.Count;
            int numVisibleInstances = visibleRenderData.Count;

            // 分配内存
            drawCommands->drawCommands = (BatchDrawCommand*)UnsafeUtility.Malloc(
                UnsafeUtility.SizeOf<BatchDrawCommand>() * numDrawCommands, alignment, Allocator.TempJob);
            drawCommands->drawRanges = (BatchDrawRange*)UnsafeUtility.Malloc(
                UnsafeUtility.SizeOf<BatchDrawRange>(), alignment, Allocator.TempJob);
            drawCommands->visibleInstances = (int*)UnsafeUtility.Malloc(
                numVisibleInstances * sizeof(int), alignment, Allocator.TempJob);

            drawCommands->drawCommandCount = numDrawCommands;
            drawCommands->drawRangeCount = 1;
            drawCommands->visibleInstanceCount = numVisibleInstances;

            // 设置绘制范围
            drawCommands->drawRanges[0].drawCommandsBegin = 0;
            drawCommands->drawRanges[0].drawCommandsCount = (uint)numDrawCommands;
            drawCommands->drawRanges[0].filterSettings = new BatchFilterSettings
            {
                renderingLayerMask = 0xffffffff,
                layer = 0,
                motionMode = MotionVectorGenerationMode.Camera,
                shadowCastingMode = ShadowCastingMode.Off,
                receiveShadows = false,
                staticShadowCaster = false,
                allDepthSorted = false
            };

            // 填充可见实例索引
            for (int i = 0; i < numVisibleInstances; i++)
            {
                drawCommands->visibleInstances[i] = i;
            }

            // 填充绘制命令
            for (int i = 0; i < numDrawCommands; i++)
            {
                var renderData = visibleRenderData[i];

                drawCommands->drawCommands[i].visibleOffset = (uint)i;
                drawCommands->drawCommands[i].visibleCount = 1;
                drawCommands->drawCommands[i].batchID = renderData.batchId;
                drawCommands->drawCommands[i].materialID = renderData.materialId;
                drawCommands->drawCommands[i].meshID = renderData.meshId;
                drawCommands->drawCommands[i].submeshIndex = 0;
                drawCommands->drawCommands[i].splitVisibilityMask = 0xff;
                drawCommands->drawCommands[i].flags = 0;
                drawCommands->drawCommands[i].sortingPosition = renderData.renderOrder;
            }

            return new JobHandle();
        }

        /// <summary>
        /// 主要的DrawBatch方法 - 参考SimpleBRGExample实现
        /// </summary>
        public void DrawBatch(List<FairyGUIRenderData> renderDataList)
        {
            if (!m_UseBRG || !m_IsInitialized)
            {
                // 回退到传统渲染方式
                DrawBatchFallback(renderDataList);
                return;
            }

            // 清空现有数据
            Clear();

            // 添加新的渲染数据
            foreach (var renderData in renderDataList)
            {
                if (renderData != null && renderData.mesh != null && renderData.material != null)
                {
                    AddRenderData(renderData);
                }
            }

            // BatchRendererGroup会在OnPerformCulling中自动处理渲染
        }

        /// <summary>
        /// 传统渲染方式回退
        /// </summary>
        private void DrawBatchFallback(List<FairyGUIRenderData> renderDataList)
        {
            // 按渲染顺序排序
            renderDataList.Sort((a, b) => a.renderOrder.CompareTo(b.renderOrder));

            foreach (var renderData in renderDataList)
            {
                if (renderData != null && renderData.isVisible &&
                    renderData.mesh != null && renderData.material != null)
                {
                    // 设置材质属性
                    SetMaterialProperties(renderData);

                    // 使用Graphics.DrawMesh渲染
                    Graphics.DrawMesh(
                        renderData.mesh,
                        renderData.localToWorldMatrix,
                        renderData.material,
                        0, // layer
                        null, // camera
                        0, // submeshIndex
                        null, // properties
                        false, // castShadows
                        false, // receiveShadows
                        false // useLightProbes
                    );
                }
            }
        }

        /// <summary>
        /// 设置材质属性（用于回退渲染）
        /// </summary>
        private void SetMaterialProperties(FairyGUIRenderData renderData)
        {
            var material = renderData.material;

            switch (renderData.shaderType)
            {
                case "Image":
                    material.SetFloat(s_BaseColorPropertyId, renderData.baseColor);
                    material.SetVector(s_ClipBoxPropertyId, renderData.clipBox);
                    break;

                case "ImageRT":
                    material.SetFloat(s_BaseColorPropertyId, renderData.baseColor);
                    material.SetVector(s_ClipBoxPropertyId, renderData.clipBox);
                    material.SetFloat(s_Linera2GammaPropertyId, renderData.linera2Gamma);
                    break;

                case "TMP":
                    material.SetColor(s_FaceColorPropertyId, renderData.faceColor);
                    material.SetColor(s_OutlineColorPropertyId, renderData.outlineColor);
                    material.SetColor(s_UnderlayColorPropertyId, renderData.underlayColor);
                    material.SetFloat(s_FaceDilatePropertyId, renderData.faceDilate);
                    material.SetFloat(s_OutlineWidthPropertyId, renderData.outlineWidth);
                    material.SetFloat(s_OutlineSoftnessPropertyId, renderData.outlineSoftness);
                    material.SetFloat(s_VertexOffsetXPropertyId, renderData.vertexOffsetX);
                    material.SetFloat(s_VertexOffsetYPropertyId, renderData.vertexOffsetY);
                    material.SetVector(s_ClipBoxPropertyId, renderData.clipBox);
                    break;
            }
        }

        /// <summary>
        /// 更新渲染数据
        /// </summary>
        public void UpdateRenderData(FairyGUIRenderData renderData)
        {
            if (renderData?.instanceData != null)
            {
                PopulateInstanceData(renderData);
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Clear();

            if (m_BRG != null)
            {
                m_BRG.Dispose();
                m_BRG = null;
            }

            m_IsInitialized = false;
        }
    }
}
