using UnityEngine;

namespace Sultan.Test
{
    /// <summary>
    /// 测试DOTS版本的FairyGUI shader是否正确编译和工作
    /// </summary>
    public class DOTSShaderTest : MonoBehaviour
    {
        [Header("DOTS Shader Testing")]
        public Shader fairyGUIImageDOTS;
        public Shader fairyGUIImageRTDOTS;
        public Shader fairyGUITMPDOTS;

        [Header("Test Materials")]
        public Material testMaterialImage;
        public Material testMaterialImageRT;
        public Material testMaterialTMP;
        
        [Header("Test Objects")]
        public GameObject testQuad;
        
        void Start()
        {
            TestShadersCompilation();
            CreateTestMaterials();
        }
        
        void TestShadersCompilation()
        {
            // 查找DOTS版本的shader
            if (fairyGUIImageDOTS == null)
            {
                fairyGUIImageDOTS = Shader.Find("FairyGUI/Image-DOTS");
            }

            if (fairyGUIImageRTDOTS == null)
            {
                fairyGUIImageRTDOTS = Shader.Find("FairyGUI/ImageRT-DOTS");
            }

            if (fairyGUITMPDOTS == null)
            {
                fairyGUITMPDOTS = Shader.Find("FairyGUI/TextMeshPro/Distance Field-DOTS");
            }

            // 检查shader是否找到并编译成功
            if (fairyGUIImageDOTS != null)
            {
                Debug.Log($"FairyGUI/Image-DOTS shader found. Supported: {fairyGUIImageDOTS.isSupported}");

                // 检查是否有DOTS_INSTANCING_ON变体
                bool hasDOTSVariant = fairyGUIImageDOTS.FindPassTagValue(0, new ShaderTagId("LightMode")) != -1;
                Debug.Log($"FairyGUI/Image-DOTS has pass: {hasDOTSVariant}");
            }
            else
            {
                Debug.LogError("FairyGUI/Image-DOTS shader not found!");
            }

            if (fairyGUIImageRTDOTS != null)
            {
                Debug.Log($"FairyGUI/ImageRT-DOTS shader found. Supported: {fairyGUIImageRTDOTS.isSupported}");

                bool hasDOTSVariant = fairyGUIImageRTDOTS.FindPassTagValue(0, new ShaderTagId("LightMode")) != -1;
                Debug.Log($"FairyGUI/ImageRT-DOTS has pass: {hasDOTSVariant}");
            }
            else
            {
                Debug.LogError("FairyGUI/ImageRT-DOTS shader not found!");
            }

            if (fairyGUITMPDOTS != null)
            {
                Debug.Log($"FairyGUI/TextMeshPro/Distance Field-DOTS shader found. Supported: {fairyGUITMPDOTS.isSupported}");

                bool hasDOTSVariant = fairyGUITMPDOTS.FindPassTagValue(0, new ShaderTagId("LightMode")) != -1;
                Debug.Log($"FairyGUI/TextMeshPro/Distance Field-DOTS has pass: {hasDOTSVariant}");
            }
            else
            {
                Debug.LogError("FairyGUI/TextMeshPro/Distance Field-DOTS shader not found!");
            }
        }
        
        void CreateTestMaterials()
        {
            // 创建测试材质
            if (fairyGUIImageDOTS != null && testMaterialImage == null)
            {
                testMaterialImage = new Material(fairyGUIImageDOTS);
                testMaterialImage.name = "TestMaterial_FairyGUI_Image_DOTS";

                // 设置一些基本属性
                testMaterialImage.SetFloat("_BaseColor", 0.0f);
                testMaterialImage.SetVector("_ClipBox", new Vector4(-2, -2, 0, 0));

                Debug.Log("Created test material for FairyGUI/Image-DOTS");
            }

            if (fairyGUIImageRTDOTS != null && testMaterialImageRT == null)
            {
                testMaterialImageRT = new Material(fairyGUIImageRTDOTS);
                testMaterialImageRT.name = "TestMaterial_FairyGUI_ImageRT_DOTS";

                // 设置一些基本属性
                testMaterialImageRT.SetFloat("_BaseColor", 0.0f);
                testMaterialImageRT.SetFloat("_Linera2Gamma", 0.0f);
                testMaterialImageRT.SetVector("_ClipBox", new Vector4(-2, -2, 0, 0));

                Debug.Log("Created test material for FairyGUI/ImageRT-DOTS");
            }

            if (fairyGUITMPDOTS != null && testMaterialTMP == null)
            {
                testMaterialTMP = new Material(fairyGUITMPDOTS);
                testMaterialTMP.name = "TestMaterial_FairyGUI_TMP_DOTS";

                // 设置一些基本属性
                testMaterialTMP.SetColor("_FaceColor", Color.white);
                testMaterialTMP.SetColor("_OutlineColor", Color.black);
                testMaterialTMP.SetFloat("_OutlineWidth", 0.0f);
                testMaterialTMP.SetFloat("_FaceDilate", 0.0f);
                testMaterialTMP.SetVector("_ClipBox", new Vector4(-2, -2, 0, 0));

                Debug.Log("Created test material for FairyGUI/TextMeshPro/Distance Field-DOTS");
            }
        }
        
        [ContextMenu("Apply Test Material to Quad")]
        void ApplyTestMaterialToQuad()
        {
            if (testQuad != null && testMaterialImage != null)
            {
                var renderer = testQuad.GetComponent<Renderer>();
                if (renderer != null)
                {
                    renderer.material = testMaterialImage;
                    Debug.Log("Applied FairyGUI/Image-DOTS material to test quad");
                }
            }
        }
        
        [ContextMenu("Test Shader Keywords")]
        void TestShaderKeywords()
        {
            if (testMaterialImage != null)
            {
                Debug.Log("Testing shader keywords for FairyGUI/Image-DOTS:");

                // 测试不同的关键字组合
                string[] keywords = { "NOT_GRAYED", "GRAYED", "COLOR_FILTER", "NOT_CLIPPED", "CLIPPED", "SOFT_CLIPPED", "ALPHA_MASK" };

                foreach (string keyword in keywords)
                {
                    bool isEnabled = testMaterialImage.IsKeywordEnabled(keyword);
                    Debug.Log($"Keyword '{keyword}': {(isEnabled ? "Enabled" : "Disabled")}");
                }
            }
        }

        [ContextMenu("Test TMP Shader Keywords")]
        void TestTMPShaderKeywords()
        {
            if (testMaterialTMP != null)
            {
                Debug.Log("Testing shader keywords for FairyGUI/TextMeshPro/Distance Field-DOTS:");

                // 测试TMP shader的关键字
                string[] keywords = { "NOT_GRAYED", "GRAYED", "NOT_CLIPPED", "CLIPPED", "OUTLINE_ON", "UNDERLAY_ON", "UNDERLAY_INNER", "ENABLE_HUD_TEXT", "ENABLE_WORLD_HUD_SCALE" };

                foreach (string keyword in keywords)
                {
                    bool isEnabled = testMaterialTMP.IsKeywordEnabled(keyword);
                    Debug.Log($"TMP Keyword '{keyword}': {(isEnabled ? "Enabled" : "Disabled")}");
                }
            }
        }

        [ContextMenu("Apply TMP Material to Quad")]
        void ApplyTMPMaterialToQuad()
        {
            if (testQuad != null && testMaterialTMP != null)
            {
                var renderer = testQuad.GetComponent<Renderer>();
                if (renderer != null)
                {
                    renderer.material = testMaterialTMP;
                    Debug.Log("Applied FairyGUI/TextMeshPro/Distance Field-DOTS material to test quad");
                }
            }
        }
        
        void OnValidate()
        {
            // 在Inspector中修改时自动查找shader
            if (fairyGUIImageDOTS == null)
            {
                fairyGUIImageDOTS = Shader.Find("FairyGUI/Image-DOTS");
            }

            if (fairyGUIImageRTDOTS == null)
            {
                fairyGUIImageRTDOTS = Shader.Find("FairyGUI/ImageRT-DOTS");
            }

            if (fairyGUITMPDOTS == null)
            {
                fairyGUITMPDOTS = Shader.Find("FairyGUI/TextMeshPro/Distance Field-DOTS");
            }
        }
    }
}
