Shader "FairyGUI/Image URP"
{
    Properties
    {
        _MainTex ("Base (RGB), Alpha (A)", 2D) = "black" {}

        _StencilComp ("Stencil Comparison", Float) = 8
        _Stencil ("Stencil ID", Float) = 0
        _StencilOp ("Stencil Operation", Float) = 0
        _StencilWriteMask ("Stencil Write Mask", Float) = 255
        _StencilReadMask ("Stencil Read Mask", Float) = 255

        _ColorMask ("Color Mask", Float) = 15

        _BlendSrcFactor ("Blend SrcFactor", Float) = 5
        _BlendDstFactor ("Blend DstFactor", Float) = 10
    }
    
    SubShader
    {
        LOD 100

        Tags
        {
            "Queue" = "Transparent"
            "IgnoreProjector" = "True"
            "RenderType" = "Transparent"
            "RenderPipeline" = "UniversalPipeline"
        }
        
        Stencil
        {
            Ref [_Stencil]
            Comp [_StencilComp]
            Pass [_StencilOp] 
            ReadMask [_StencilReadMask]
            WriteMask [_StencilWriteMask]
        }

        Cull Off
        Lighting Off
        ZWrite Off
        Blend [_BlendSrcFactor] [_BlendDstFactor], One One
        ColorMask [_ColorMask]

        Pass
        {
            Name "FairyGUI Image"
            
            HLSLPROGRAM
            #pragma target 4.5
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

            #pragma multi_compile NOT_COMBINED COMBINED
            #pragma multi_compile NOT_GRAYED GRAYED COLOR_FILTER
            #pragma multi_compile NOT_CLIPPED CLIPPED SOFT_CLIPPED ALPHA_MASK
            #pragma multi_compile _ DOTS_INSTANCING_ON
            #pragma vertex vert
            #pragma fragment frag

            #ifdef DOTS_INSTANCING_ON
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/UnityInstancing.hlsl"
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/SpaceTransforms.hlsl"
            #endif

            #ifdef DOTS_INSTANCING_ON
                UNITY_DOTS_INSTANCING_START(MaterialPropertyMetadata)
                    UNITY_DOTS_INSTANCED_PROP(float4, _Color)
                UNITY_DOTS_INSTANCING_END(MaterialPropertyMetadata)
                #define _Color UNITY_ACCESS_DOTS_INSTANCED_PROP_WITH_DEFAULT(float4, _Color)
            #endif
            
            struct Attributes
            {
                float4 positionOS : POSITION;
                float4 color : COLOR;
                float4 uv : TEXCOORD0;
                #ifdef DOTS_INSTANCING_ON
                UNITY_VERTEX_INPUT_INSTANCE_ID
                #endif
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float4 color : COLOR;
                float4 uv : TEXCOORD0;

                #if defined(CLIPPED) || defined(SOFT_CLIPPED)
                float2 clipPos : TEXCOORD1;
                #endif

                #ifdef DOTS_INSTANCING_ON
                UNITY_VERTEX_INPUT_INSTANCE_ID
                #endif
            };

            TEXTURE2D(_MainTex);
            SAMPLER(sampler_MainTex);
            
            #ifdef COMBINED
            TEXTURE2D(_AlphaTex);
            SAMPLER(sampler_AlphaTex);
            #endif

            CBUFFER_START(UnityPerMaterial)
            float4 _MainTex_ST;
            #if defined(CLIPPED) || defined(SOFT_CLIPPED)
            float4 _ClipBox = float4(-2, -2, 0, 0);
            #endif

            #ifdef SOFT_CLIPPED
            float4 _ClipSoftness = float4(0, 0, 0, 0);
            #endif

            #ifdef COLOR_FILTER
            float4x4 _ColorMatrix;
            float4 _ColorOffset;
            float _ColorOption;
            #endif
            CBUFFER_END

            Varyings vert(Attributes input)
            {
                Varyings output = (Varyings)0;

                #ifdef DOTS_INSTANCING_ON
                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_TRANSFER_INSTANCE_ID(input, output);
                #endif

                output.positionCS = TransformObjectToHClip(input.positionOS.xyz);
                output.uv = input.uv;
                
                #if !defined(UNITY_COLORSPACE_GAMMA)
                output.color.rgb = input.color.rgb;
                output.color.a = input.color.a;
                #else
                output.color = input.color;
                #endif

                #if defined(CLIPPED) || defined(SOFT_CLIPPED)
                output.clipPos = mul(GetObjectToWorldMatrix(), input.positionOS).xy * _ClipBox.zw + _ClipBox.xy;
                #endif

                return output;
            }
            
            half4 frag(Varyings input) : SV_Target
            {
                #ifdef DOTS_INSTANCING_ON
                UNITY_SETUP_INSTANCE_ID(input);
                #endif

                half4 col = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, input.uv.xy / input.uv.w) * input.color;

                #ifdef COMBINED
                col.a *= SAMPLE_TEXTURE2D(_AlphaTex, sampler_AlphaTex, input.uv.xy / input.uv.w).g;
                #endif

                #ifdef GRAYED
                half grey = dot(col.rgb, half3(0.299, 0.587, 0.114));
                col.rgb = half3(grey, grey, grey);
                #endif

                #ifdef SOFT_CLIPPED
                float2 factor = float2(0,0);
                if(input.clipPos.x<0)
                    factor.x = (1.0-abs(input.clipPos.x)) * _ClipSoftness.x;
                else
                    factor.x = (1.0-input.clipPos.x) * _ClipSoftness.z;
                if(input.clipPos.y<0)
                    factor.y = (1.0-abs(input.clipPos.y)) * _ClipSoftness.w;
                else
                    factor.y = (1.0-input.clipPos.y) * _ClipSoftness.y;
                col.a *= clamp(min(factor.x, factor.y), 0.0, 1.0);
                #endif

                #ifdef CLIPPED
                float2 factor = abs(input.clipPos);
                col.a *= step(max(factor.x, factor.y), 1);
                #endif

                #ifdef COLOR_FILTER
                if (_ColorOption == 0)
                {
                    half4 col2 = col;
                    col2.r = dot(col, _ColorMatrix[0]) + _ColorOffset.x;
                    col2.g = dot(col, _ColorMatrix[1]) + _ColorOffset.y;
                    col2.b = dot(col, _ColorMatrix[2]) + _ColorOffset.z;
                    col2.a = dot(col, _ColorMatrix[3]) + _ColorOffset.w;
                    col = col2;
                }
                else //premultiply alpha
                    col.rgb *= col.a;
                #endif

                #ifdef ALPHA_MASK
                clip(col.a - 0.001);
                #endif

                return col;
            }
            ENDHLSL
        }
    }
}