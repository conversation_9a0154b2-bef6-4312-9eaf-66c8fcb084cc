# FairyGUI DOTS Instancing Shaders

这个文件夹包含了支持DOTS instancing的FairyGUI shader版本，专门为BatchRendererGroup (BRG) 渲染优化而设计。

## 包含的Shader

### 1. FairyGUI-Image-DOTS.shader
- **Shader名称**: `FairyGUI/Image-DOTS`
- **基于**: `FairyGUI/Image`
- **用途**: 支持DOTS instancing的基础图像渲染

### 2. FairyGUI-ImageRT-DOTS.shader
- **Shader名称**: `FairyGUI/ImageRT-DOTS`
- **基于**: `FairyGUI/ImageRT`
- **用途**: 支持DOTS instancing的渲染纹理图像渲染，包含Linear到Gamma转换

## 主要特性

### DOTS Instancing支持
- 添加了 `#pragma multi_compile _ DOTS_INSTANCING_ON`
- 添加了 `#pragma multi_compile_instancing`
- 添加了 `#pragma instancing_options renderinglayer`
- 支持BatchRendererGroup渲染

### 保留的原有功能
- 所有原始的multi_compile指令（GRAYED, CLIPPED等）
- 完整的Stencil支持
- 颜色过滤器支持
- 软裁剪和硬裁剪支持
- Alpha遮罩支持

### 实例化属性
以下属性支持per-instance设置：
- `_BaseColor`: 基础颜色偏移
- `_ClipBox`: 裁剪区域
- `_Linera2Gamma`: Linear到Gamma转换系数（仅ImageRT版本）

## 使用方法

### 1. 在BatchRendererGroup中使用

```csharp
// 创建材质时使用DOTS版本的shader
Material material = new Material(Shader.Find("FairyGUI/Image-DOTS"));

// 在BatchRendererGroup中使用
BatchRendererGroup brg = new BatchRendererGroup(OnPerformCulling, IntPtr.Zero);
```

### 2. 材质设置

```csharp
// 设置基本属性
material.SetFloat("_BaseColor", 0.0f);
material.SetVector("_ClipBox", new Vector4(-2, -2, 0, 0));

// 对于ImageRT版本
material.SetFloat("_Linera2Gamma", 0.0f);
```

### 3. 关键字控制

```csharp
// 启用灰度效果
material.EnableKeyword("GRAYED");

// 启用裁剪
material.EnableKeyword("CLIPPED");

// 启用软裁剪
material.EnableKeyword("SOFT_CLIPPED");

// 启用颜色过滤器
material.EnableKeyword("COLOR_FILTER");

// 启用Alpha遮罩
material.EnableKeyword("ALPHA_MASK");
```

## 技术细节

### DOTS Instancing实现
- 使用`UNITY_VERTEX_INPUT_INSTANCE_ID`宏在结构体中添加实例ID
- 使用`UNITY_SETUP_INSTANCE_ID`和`UNITY_TRANSFER_INSTANCE_ID`宏处理实例数据
- 使用`UNITY_DOTS_INSTANCING_START/END`宏包装实例化属性
- 使用`UNITY_ACCESS_DOTS_INSTANCED_PROP_WITH_DEFAULT`访问实例属性

### 兼容性
- 目标平台: 4.5+
- 排除渲染器: gles, gles3, glcore
- 渲染管线: Universal Render Pipeline (URP)
- Unity版本: 2022.3+

### 性能优化
- 支持SRP Batch兼容的cbuffer布局
- 优化的实例数据访问
- 保持与原始shader相同的渲染状态

## 测试

使用提供的`DOTSShaderTest.cs`脚本来验证shader的编译和功能：

1. 将脚本添加到场景中的GameObject
2. 运行场景查看Console输出
3. 使用Context Menu测试不同功能

## 注意事项

1. **BatchRendererGroup要求**: 这些shader专门为BRG设计，需要正确的实例数据设置
2. **属性限制**: 只有在DOTS instancing块中声明的属性才能per-instance设置
3. **关键字管理**: 确保正确管理shader关键字以避免不必要的变体
4. **性能**: 虽然支持DOTS instancing，但仍需要合理的批次大小和实例数据管理

## 故障排除

### Shader编译错误
- 确保Unity版本支持DOTS instancing
- 检查URP包是否正确安装
- 验证target 4.5+设置

### 渲染问题
- 确保材质使用正确的shader变体
- 检查BatchRendererGroup的实例数据设置
- 验证cbuffer数据对齐

### 性能问题
- 监控shader变体数量
- 优化实例数据大小
- 合理设置批次大小
