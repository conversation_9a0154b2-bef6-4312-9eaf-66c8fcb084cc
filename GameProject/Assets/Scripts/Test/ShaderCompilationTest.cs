using UnityEngine;

namespace Sultan.Test
{
    /// <summary>
    /// 测试DOTS shader编译状态
    /// </summary>
    public class ShaderCompilationTest : MonoBehaviour
    {
        [Header("DOTS Shaders")]
        public Shader fairyGUIImageDOTS;
        public Shader fairyGUIImageRTDOTS;
        public Shader fairyGUITMPDOTS;
        
        void Start()
        {
            TestShaderCompilation();
        }
        
        void TestShaderCompilation()
        {
            Debug.Log("=== DOTS Shader Compilation Test ===");
            
            // 测试FairyGUI/Image-DOTS
            fairyGUIImageDOTS = Shader.Find("FairyGUI/Image-DOTS");
            if (fairyGUIImageDOTS != null)
            {
                Debug.Log($"✓ FairyGUI/Image-DOTS found. Supported: {fairyGUIImageDOTS.isSupported}");
                if (!fairyGUIImageDOTS.isSupported)
                {
                    Debug.LogError("✗ FairyGUI/Image-DOTS is not supported on this platform!");
                }
            }
            else
            {
                Debug.LogError("✗ FairyGUI/Image-DOTS shader not found!");
            }
            
            // 测试FairyGUI/ImageRT-DOTS
            fairyGUIImageRTDOTS = Shader.Find("FairyGUI/ImageRT-DOTS");
            if (fairyGUIImageRTDOTS != null)
            {
                Debug.Log($"✓ FairyGUI/ImageRT-DOTS found. Supported: {fairyGUIImageRTDOTS.isSupported}");
                if (!fairyGUIImageRTDOTS.isSupported)
                {
                    Debug.LogError("✗ FairyGUI/ImageRT-DOTS is not supported on this platform!");
                }
            }
            else
            {
                Debug.LogError("✗ FairyGUI/ImageRT-DOTS shader not found!");
            }
            
            // 测试FairyGUI/TextMeshPro/Distance Field-DOTS
            fairyGUITMPDOTS = Shader.Find("FairyGUI/TextMeshPro/Distance Field-DOTS");
            if (fairyGUITMPDOTS != null)
            {
                Debug.Log($"✓ FairyGUI/TextMeshPro/Distance Field-DOTS found. Supported: {fairyGUITMPDOTS.isSupported}");
                if (!fairyGUITMPDOTS.isSupported)
                {
                    Debug.LogError("✗ FairyGUI/TextMeshPro/Distance Field-DOTS is not supported on this platform!");
                }
            }
            else
            {
                Debug.LogError("✗ FairyGUI/TextMeshPro/Distance Field-DOTS shader not found!");
            }
            
            // 测试材质创建
            TestMaterialCreation();
        }
        
        void TestMaterialCreation()
        {
            Debug.Log("=== Material Creation Test ===");
            
            // 测试Image DOTS材质
            if (fairyGUIImageDOTS != null)
            {
                try
                {
                    var material = new Material(fairyGUIImageDOTS);
                    material.SetFloat("_BaseColor", 0.0f);
                    material.SetVector("_ClipBox", new Vector4(-2, -2, 0, 0));
                    Debug.Log("✓ FairyGUI/Image-DOTS material created successfully");
                    DestroyImmediate(material);
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"✗ Failed to create FairyGUI/Image-DOTS material: {e.Message}");
                }
            }
            
            // 测试ImageRT DOTS材质
            if (fairyGUIImageRTDOTS != null)
            {
                try
                {
                    var material = new Material(fairyGUIImageRTDOTS);
                    material.SetFloat("_BaseColor", 0.0f);
                    material.SetFloat("_Linera2Gamma", 0.0f);
                    material.SetVector("_ClipBox", new Vector4(-2, -2, 0, 0));
                    Debug.Log("✓ FairyGUI/ImageRT-DOTS material created successfully");
                    DestroyImmediate(material);
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"✗ Failed to create FairyGUI/ImageRT-DOTS material: {e.Message}");
                }
            }
            
            // 测试TMP DOTS材质
            if (fairyGUITMPDOTS != null)
            {
                try
                {
                    var material = new Material(fairyGUITMPDOTS);
                    material.SetColor("_FaceColor", Color.white);
                    material.SetColor("_OutlineColor", Color.black);
                    material.SetFloat("_OutlineWidth", 0.0f);
                    material.SetFloat("_FaceDilate", 0.0f);
                    material.SetVector("_ClipBox", new Vector4(-2, -2, 0, 0));
                    Debug.Log("✓ FairyGUI/TextMeshPro/Distance Field-DOTS material created successfully");
                    DestroyImmediate(material);
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"✗ Failed to create FairyGUI/TextMeshPro/Distance Field-DOTS material: {e.Message}");
                }
            }
        }
        
        [ContextMenu("Test Shader Keywords")]
        void TestShaderKeywords()
        {
            Debug.Log("=== Shader Keywords Test ===");
            
            if (fairyGUITMPDOTS != null)
            {
                var material = new Material(fairyGUITMPDOTS);
                
                // 测试不同的关键字组合
                string[] keywords = { 
                    "NOT_GRAYED", "GRAYED", 
                    "NOT_CLIPPED", "CLIPPED", 
                    "OUTLINE_ON", 
                    "UNDERLAY_ON", "UNDERLAY_INNER",
                    "DOTS_INSTANCING_ON"
                };
                
                foreach (string keyword in keywords)
                {
                    try
                    {
                        material.EnableKeyword(keyword);
                        Debug.Log($"✓ Keyword '{keyword}' enabled successfully");
                        material.DisableKeyword(keyword);
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogError($"✗ Failed to enable keyword '{keyword}': {e.Message}");
                    }
                }
                
                DestroyImmediate(material);
            }
        }
        
        [ContextMenu("Force Shader Compilation")]
        void ForceShaderCompilation()
        {
            Debug.Log("=== Force Shader Compilation ===");
            
            if (fairyGUITMPDOTS != null)
            {
                // 强制编译所有变体
                var material = new Material(fairyGUITMPDOTS);
                
                // 启用DOTS instancing
                material.EnableKeyword("DOTS_INSTANCING_ON");
                material.EnableKeyword("OUTLINE_ON");
                material.EnableKeyword("UNDERLAY_ON");
                
                // 创建一个临时的渲染纹理来强制编译
                var rt = RenderTexture.GetTemporary(1, 1);
                Graphics.Blit(Texture2D.whiteTexture, rt, material);
                RenderTexture.ReleaseTemporary(rt);
                
                Debug.Log("✓ Forced shader compilation completed");
                DestroyImmediate(material);
            }
        }
        
        void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 400, 300));
            GUILayout.Label("DOTS Shader Compilation Test");
            
            GUILayout.Label($"Image DOTS: {(fairyGUIImageDOTS != null ? "✓" : "✗")}");
            GUILayout.Label($"ImageRT DOTS: {(fairyGUIImageRTDOTS != null ? "✓" : "✗")}");
            GUILayout.Label($"TMP DOTS: {(fairyGUITMPDOTS != null ? "✓" : "✗")}");
            
            if (GUILayout.Button("Test Shader Keywords"))
            {
                TestShaderKeywords();
            }
            
            if (GUILayout.Button("Force Shader Compilation"))
            {
                ForceShaderCompilation();
            }
            
            if (GUILayout.Button("Retest All"))
            {
                TestShaderCompilation();
            }
            
            GUILayout.EndArea();
        }
    }
}
