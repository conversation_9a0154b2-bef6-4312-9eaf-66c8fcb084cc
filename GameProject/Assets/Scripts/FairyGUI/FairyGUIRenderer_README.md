# FairyGUI BatchRendererGroup 实现

这个实现基于SimpleBRGExample.cs，为FairyGUI提供了高效的BatchRendererGroup渲染支持。

## 核心组件

### 1. FairyGUIRenderData
渲染数据结构，包含：
- **基础属性**: mesh, material, localToWorldMatrix, renderOrder
- **FairyGUI属性**: color, clipBox, baseColor, linera2Gamma
- **TMP属性**: faceColor, outlineColor, underlayColor等
- **BatchRendererGroup属性**: batchId, meshId, materialId, instanceData

### 2. FairyGUIRenderer
主要的渲染器类，提供：
- **BatchRendererGroup管理**: 初始化、注册、销毁
- **渲染数据管理**: 添加、移除、更新
- **DrawBatch方法**: 主要的批量渲染接口
- **回退渲染**: 当BRG不可用时使用传统Graphics.DrawMesh

## 使用方法

### 基本使用

```csharp
// 1. 创建渲染器
var renderer = new FairyGUIRenderer(useBRG: true);

// 2. 创建渲染数据
var renderData = new FairyGUIRenderData
{
    mesh = yourMesh,
    material = yourDOTSMaterial, // 使用DOTS版本的shader
    localToWorldMatrix = Matrix4x4.identity,
    renderOrder = 0,
    shaderType = "Image", // "Image", "ImageRT", "TMP"
    isVisible = true
};

// 3. 设置shader特定属性
switch (renderData.shaderType)
{
    case "Image":
        renderData.baseColor = 0f;
        renderData.clipBox = new Vector4(-2, -2, 0, 0);
        break;
    case "TMP":
        renderData.faceColor = Color.white;
        renderData.outlineColor = Color.black;
        renderData.outlineWidth = 0.1f;
        break;
}

// 4. 批量渲染
var renderDataList = new List<FairyGUIRenderData> { renderData };
renderer.DrawBatch(renderDataList);

// 5. 清理
renderer.Dispose();
```

### 高级使用

```csharp
// 动态更新渲染数据
renderData.localToWorldMatrix = newMatrix;
renderData.faceColor = newColor;
renderer.UpdateRenderData(renderData);

// 添加/移除单个渲染数据
renderer.AddRenderData(renderData);
renderer.RemoveRenderData(renderData);

// 清空所有渲染数据
renderer.Clear();
```

## Shader支持

### 支持的Shader类型

1. **FairyGUI/Image-DOTS**
   - 基础图像渲染
   - 支持属性: _BaseColor, _ClipBox

2. **FairyGUI/ImageRT-DOTS**
   - 渲染纹理图像渲染
   - 支持属性: _BaseColor, _ClipBox, _Linera2Gamma

3. **FairyGUI/TextMeshPro/Distance Field-DOTS**
   - TextMeshPro SDF文本渲染
   - 支持属性: _FaceColor, _OutlineColor, _UnderlayColor等

### 实例化属性

每个渲染实例可以有不同的属性值：

```csharp
// Image shader
renderData.baseColor = 0.1f;
renderData.clipBox = new Vector4(-1, -1, 2, 2);

// TMP shader
renderData.faceColor = Color.red;
renderData.outlineColor = Color.black;
renderData.outlineWidth = 0.05f;
renderData.faceDilate = 0.1f;
renderData.vertexOffsetX = 1.0f;
renderData.vertexOffsetY = 0.5f;
```

## 性能优化

### BatchRendererGroup优势
- **GPU Instancing**: 大幅减少draw call
- **内存效率**: 实例数据直接传递给GPU
- **渲染顺序**: 保持FairyGUI的原有渲染顺序
- **动态更新**: 支持实时更新实例数据

### 最佳实践

1. **批次大小**: 每个批次建议100-1000个实例
2. **材质管理**: 相同材质的对象会自动批处理
3. **网格复用**: 尽可能复用相同的网格
4. **属性更新**: 只在必要时更新实例数据

```csharp
// 好的做法：批量更新
var renderDataList = new List<FairyGUIRenderData>();
foreach (var uiElement in uiElements)
{
    var renderData = CreateRenderData(uiElement);
    renderDataList.Add(renderData);
}
renderer.DrawBatch(renderDataList);

// 避免：频繁的单个更新
foreach (var uiElement in uiElements)
{
    var renderData = CreateRenderData(uiElement);
    renderer.DrawBatch(new List<FairyGUIRenderData> { renderData });
}
```

## 错误处理

### 常见问题

1. **Shader不支持**: 确保使用DOTS版本的shader
2. **实例数据错误**: 检查MetadataValue的对齐和大小
3. **渲染顺序错误**: 确保renderOrder正确设置
4. **内存泄漏**: 及时调用Dispose()释放资源

### 调试技巧

```csharp
// 启用调试日志
Debug.Log($"Registered mesh: {renderData.meshId}, material: {renderData.materialId}");

// 检查BRG状态
if (!renderer.IsInitialized)
{
    Debug.LogError("BatchRendererGroup initialization failed");
}

// 性能测试
var stopwatch = System.Diagnostics.Stopwatch.StartNew();
renderer.DrawBatch(renderDataList);
stopwatch.Stop();
Debug.Log($"DrawBatch took {stopwatch.ElapsedMilliseconds}ms");
```

## 与FairyGUI集成

### 集成步骤

1. **替换渲染器**: 在FairyGUI的渲染管线中使用FairyGUIRenderer
2. **转换数据**: 将FairyGUI的渲染数据转换为FairyGUIRenderData
3. **管理生命周期**: 在适当的时机创建和销毁渲染器
4. **处理事件**: 响应FairyGUI的显示/隐藏事件

```csharp
// 示例集成代码
public class FairyGUIBatchManager : MonoBehaviour
{
    private FairyGUIRenderer m_Renderer;
    
    void Start()
    {
        m_Renderer = new FairyGUIRenderer(true);
        
        // 监听FairyGUI事件
        Stage.inst.onPostUpdate += OnFairyGUIPostUpdate;
    }
    
    void OnFairyGUIPostUpdate()
    {
        // 收集FairyGUI渲染数据
        var renderDataList = CollectFairyGUIRenderData();
        
        // 批量渲染
        m_Renderer.DrawBatch(renderDataList);
    }
    
    void OnDestroy()
    {
        m_Renderer?.Dispose();
        Stage.inst.onPostUpdate -= OnFairyGUIPostUpdate;
    }
}
```

## 测试和验证

使用提供的`FairyGUIBatchExample.cs`进行测试：

1. **基础功能测试**: 验证渲染正确性
2. **性能测试**: 对比BRG和传统渲染的性能
3. **压力测试**: 测试大量对象的渲染性能
4. **兼容性测试**: 验证不同shader的兼容性

## 注意事项

1. **Unity版本**: 需要Unity 2022.3+
2. **渲染管线**: 需要URP
3. **平台支持**: 排除gles, gles3, glcore
4. **内存管理**: 及时释放GraphicsBuffer资源
5. **线程安全**: BatchRendererGroup的回调在渲染线程执行

## 扩展性

这个实现可以轻松扩展以支持：
- 更多的FairyGUI shader类型
- 自定义的实例化属性
- 动态LOD系统
- 视锥体裁剪优化
- 遮挡剔除集成
